'use client';

import React, { useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';

import { blogPosts } from '@/data/blogPosts';
import TestimonialSlider from '@/components/TestimonialSlider';
import { 
  getOrganizationStructuredData, 
  getYogaInstructorStructuredData,
  getFAQStructuredData 
} from '@/lib/yogaStructuredData';

// Simple Icon component
const SafeIcon = React.memo(({ Icon, className = '' }) => {
  if (!Icon) return null;
  return <Icon className={`w-6 h-6 ${className}`} />;
});
SafeIcon.displayName = 'SafeIcon';

// Transform blog titles to poetic versions
const transformTitleToPoetic = (title) => {
  const poeticTitles = {
    'Stanie na Rękach': 'Świat do góry nogami',
    'Szpagaty': 'Elastyczność duszy',
    'Kobieca Siła': 'Wewnętrzna bogini',
    '<PERSON>ga dla Początkujących': '<PERSON><PERSON><PERSON> kroki na ścieżce',
    'Medytacja': '<PERSON><PERSON>za w sercu',
    'Pranayama': '<PERSON><PERSON><PERSON> oddechu',
    'Asany': '<PERSON>zja ciała',
    'Mindfulness': 'Obecność chwili'
  };

  return poeticTitles[title] || title;
};



// =============================================
// ENTERPRISE 11/10 HERO SECTION - ULTRA-MINIMAL PERFECTION
// Sacred simplicity meets Balinese elegance
// =============================================

const EnterpriseHeroSection = React.memo(() => {
  return (
    <section className="hero">
      <div className="hero-content">
        {/* BAKASANA TITLE - Ultra-minimal whisper */}
        <h1 className="hero-title">
          BAKASANA
        </h1>

        {/* ENHANCED SUBTITLE - More spiritual */}
        <p className="hero-subtitle">
          Transformacyjne podróże z jogą
        </p>

        {/* ENHANCED LOCATIONS - With spiritual elements */}
        <div className="hero-meta">
          Bali · Sri Lanka · Duchowa Transformacja
        </div>

        {/* SUBTLE CALL TO ACTION - Whisper style */}
        <div className="hero-cta">
          <Link 
            href="/program" 
            className="hero-cta-link"
          >
            Odkryj magię podróży
          </Link>
        </div>
      </div>
    </section>
  );
});
EnterpriseHeroSection.displayName = 'EnterpriseHeroSection';

// =============================================
// ENTERPRISE 11/10 RETREAT CARD - SACRED MINIMALISM
// Museum-quality design meets spiritual elegance  
// =============================================

const EnterpriseRetreatCard = React.memo(({
  title,
  description,
  link,
  imageUrl,
  location,
  duration,
  badge,
  testimonial,
  intimateDetail,
  className = ''
}) => {
  return (
    <article className={`group relative ${className}`}>
      {/* VISUAL FOUNDATION - Sacred imagery */}
      {imageUrl && (
        <div className="relative aspect-[5/4] overflow-hidden mb-8">
          <Image
            src={imageUrl}
            alt={`${title} - Sacred retreat sanctuary`}
            fill
            className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            quality={95}
          />
          
          {/* Sacred gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          
          {/* Badge - Cultural marker */}
          {badge && (
            <div className="absolute top-6 left-6">
              <span className="px-3 py-1 bg-white/95 text-charcoal text-xs font-light tracking-[0.1em] uppercase backdrop-blur-sm">
                {badge}
              </span>
            </div>
          )}
        </div>
      )}

      {/* CONTENT HIERARCHY - Sacred typography */}
      <div className="space-y-6">
        
        {/* Location & Duration - Sacred details */}
        <div className="flex items-center justify-between text-sm">
          {location && (
            <div className="flex items-center gap-2 text-sage-dark font-light tracking-wide">
              <span className="w-1 h-1 bg-sage-dark rounded-full"></span>
              <span>{location}</span>
            </div>
          )}
          {duration && (
            <span className="text-charcoal/60 font-light tracking-wide">{duration}</span>
          )}
        </div>

        {/* Title - Sacred heading */}
        <div className="space-y-3">
          <h3 className="text-2xl md:text-3xl font-light text-charcoal leading-tight tracking-wide font-serif">
            {link ? (
              <Link
                href={link}
                className="hover:text-sage-dark transition-colors duration-200"
              >
                {title}
              </Link>
            ) : (
              title
            )}
          </h3>
          
          {/* Intimate link in corner */}
          <div className="text-right">
            <a
              href={link || '#'}
              className="text-xs font-light text-stone/60 tracking-wide hover:opacity-70 transition-opacity duration-200"
            >
              Zabiorę Cię tam
            </a>
          </div>
        </div>

        {/* Description - Haiku-like brevity */}
        <p className="text-charcoal/80 font-light leading-relaxed tracking-wide max-w-md mb-4">
          {description}
        </p>

        {/* Intimate Detail */}
        {intimateDetail && (
          <p className="text-temple-gold/80 font-light italic text-sm mb-3">
            {intimateDetail}
          </p>
        )}

        {/* Testimonial Quote */}
        {testimonial && (
          <p className="text-charcoal/60 font-light text-sm italic">
            {testimonial}
          </p>
        )}

        {/* Sacred action */}
        {link && (
          <div className="pt-4">
            <Link
              href={link}
              className="group/link inline-flex items-center gap-2 text-sm font-light tracking-[0.1em] text-sage-dark hover:text-charcoal transition-colors duration-200"
            >
              <span>Chodź ze mną</span>
              <svg
                className="w-4 h-4 transition-transform duration-200 group-hover/link:translate-x-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        )}

      </div>
    </article>
  );
});
EnterpriseRetreatCard.displayName = 'EnterpriseRetreatCard';

// Ultra-Minimal Section Divider
const SectionDivider = React.memo(() => {
  return (
    <div className="section-divider my-24"></div>
  );
});
SectionDivider.displayName = 'SectionDivider';





// Main WellnessPage Component - Ultra-Minimalist
const WellnessPage = ({ latestPosts }) => {
  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);



  // Enhanced testimonials with more details
  const testimonials = useMemo(() => [
    {
      quote: "To była najbardziej transformująca podróż mojego życia. Julia stworzyła przestrzeń ciepła i bezpieczeństwa, która pozwoliła mi prawdziwie połączyć się z sobą. Wróciłam jako zupełnie nowa kobieta, pełna pewności siebie i wewnętrznego spokoju.",
      author: "Anna Kowalska",
      location: "Warszawa",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/anna.webp"
    },
    {
      quote: "Idealny balans jogi, kultury i relaksu. Julia ma dar tworzenia magicznych chwil, które zostają w pamięci na zawsze. Nasza grupa stała się jak druga rodzina - kobiety wspierające się nawzajem w drodze do siebie.",
      author: "Katarzyna Nowak",
      location: "Gdańsk",
      rating: 5,
      retreat: "Raj Gili Air",
      avatar: "/images/testimonials/kasia.webp"
    },
    {
      quote: "Każdy dzień przynosił nowe odkrycia i głębsze zrozumienie siebie. Julia prowadzi z taką mądrością i empatią, że czujesz się bezpiecznie na każdym kroku. To nie był tylko retreat - to była prawdziwa podróż do siebie.",
      author: "Marta Wiśniewska",
      location: "Wrocław",
      rating: 5,
      retreat: "Klify Canggu",
      avatar: "/images/testimonials/marta.webp"
    },
    {
      quote: "Bali z Julią to nie tylko joga - to odkrywanie swojej wewnętrznej siły i potencjału. Wróciłam z nową energią, spokojem wewnętrznym i pewnością, że mogę wszystko. Polecam każdej kobiecie, która szuka prawdziwej zmiany.",
      author: "Agnieszka Zielińska",
      location: "Kraków",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/agnieszka.webp"
    }
  ], []);

  // Enhanced FAQs with more comprehensive answers
  const faqs = useMemo(() => [
    {
      question: "Czy retreaty są odpowiednie dla początkujących w jodze?",
      answer: "Absolutnie tak! Nasze retreaty przyjmują kobiety na każdym poziomie doświadczenia. Julia prowadzi każdą sesję z uwagą na indywidualne potrzeby i możliwości. Oferujemy modyfikacje dla każdego ćwiczenia, więc każda znajdzie swoje miejsce w naszym kręgu, niezależnie od poziomu zaawansowania."
    },
    {
      question: "Co dokładnie jest wliczone w cenę retreatu?",
      answer: "Cena obejmuje pełne zakwaterowanie w starannie wybranych miejscach, wszystkie wegetariańskie/wegańskie posiłki przygotowane z lokalnych składników, codzienne sesje jogi i medytacji, wycieczki kulturalne z przewodnikiem, transfery lotniskowe, ubezpieczenie grupowe oraz wsparcie Julii przez całą podróż. Jedyne dodatkowe koszty to loty i ewentualne zakupy osobiste."
    },
    {
      question: "Jakie są terminy najbliższych retreatów?",
      answer: "Nasze nadchodzące retreaty zaplanowane są na: czerwiec 2024 (Ubud), lipiec 2024 (Gili Air), wrzesień 2024 (Canggu) oraz październik 2024 (Sri Lanka - nowość!). Szczegółowe daty i dostępność miejsc znajdziesz w sekcji kalendarza. Rezerwacja miejsc odbywa się z wyprzedzeniem 3-6 miesięcy."
    },
    {
      question: "Czy muszę mieć wcześniejsze doświadczenie w medytacji?",
      answer: "Nie, żadne wcześniejsze doświadczenie nie jest konieczne. Nasze retreaty są idealne zarówno dla osób rozpoczynających swoją duchową podróż, jak i dla bardziej zaawansowanych praktyków. Julia wprowadza techniki medytacji stopniowo, zawsze dostosowując je do grupy i indywidualnych potrzeb każdej uczestniczki."
    },
    {
      question: "Jak duże są grupy na retreatach?",
      answer: "Nasze grupy są świadomie małe i intymne - zazwyczaj 6-12 uczestniczek, maksymalnie 15 osób. To zapewnia indywidualną uwagę Julii, możliwość nawiązania głębokich, znaczących połączeń z innymi kobietami oraz stworzenie bezpiecznej przestrzeni dla osobistych przemian."
    },
    {
      question: "Jakie są warunki pogodowe i co spakować?",
      answer: "Bali i Sri Lanka cieszą się tropikalnym klimatem przez cały rok. Temperatura wynosi 26-32°C, z możliwością opadów (szczególnie październik-marzec). Przygotowujemy szczegółową listę rzeczy do spakowania dla każdej uczestniczki, uwzględniającą specyfikę konkretnego retreatu i pory roku."
    }
  ], []);

  // Minimalist retreats - Haiku-like descriptions with warmth
  const baliSriLankaRetreats = useMemo(() => [
    {
      id: 'ubud-march-2025',
      type: 'Wiosenna Transformacja',
      title: 'UBUD',
      startDate: '15 marca 2025',
      endDate: '27 marca 2025',
      location: 'Bali',
      participants: 6,
      maxParticipants: 12,
      price: '',
      originalPrice: '',
      description: 'Świt. Dżungla. Cisza.',
      testimonial: '"Najpiękniejsze miejsce na ziemi" - Kasia',
      intimateDetail: 'Tu medytujemy o świcie',
      available: true,
      status: 'early-bird',
      highlights: ['Poranne medytacje w mglistej dżungli', 'Ceremonie oczyszczenia duszy', 'Spotkania z lokalną mądrością', 'Duchowe wędrówki'],
      accommodation: 'Dom dla duszy',
      meals: 'Pokarm dla duszy',
      activities: ['Wschód słońca z jogą', 'Wędrówki do świętych miejsc', 'Spotkania z lokalnymi mistrzami', 'Rytuały oczyszczenia'],
      imageUrl: '/images/gallery/ubud-rice-terraces.webp',
      culturalElements: ['Om Swastiastu pozdrowienia', 'Balijskie ofiary Penjor', 'Nauka podstaw bahasa Indonesia']
    },
    {
      id: 'sri-lanka-june-2025',
      type: 'Perła Oceanu Indyjskiego',
      title: 'SRI LANKA',
      startDate: '10 czerwca 2025',
      endDate: '22 czerwca 2025',
      location: 'Sri Lanka',
      participants: 4,
      maxParticipants: 10,
      price: '',
      description: 'Ocean. Świątynie. Wieczność.',
      testimonial: '"Powrót do siebie" - Marta',
      intimateDetail: 'Ulubione miejsce na wschód słońca',
      available: true,
      status: 'new',
      highlights: ['Ajurwedyjskie rytuały uzdrowienia', 'Medytacje w świątyniach', 'Mądrość starożytnych mistrzów', 'Harmonia z naturą'],
      accommodation: 'Dom nad oceanem',
      meals: 'Ajurwedyjskie eliksiry życia',
      activities: ['Wschód słońca nad oceanem', 'Rytuały uzdrowienia', 'Pielgrzymki do świętych miejsc', 'Spotkania z mnichami'],
      imageUrl: '/images/gallery/sri-lanka-temple.webp',
      culturalElements: ['Ayubowan pozdrowienia', 'Ceremonie buddyjskie', 'Nauka podstaw sinhala']
    },
    {
      id: 'gili-air-september-2025',
      type: 'Rajska Odnowa',
      title: 'GILI AIR',
      startDate: '5 września 2025',
      endDate: '12 września 2025',
      location: 'Gili Air',
      participants: 3,
      maxParticipants: 8,
      price: '',
      description: 'Czas. Ocean. Cisza.',
      testimonial: '"Miejsce, gdzie oddychasz" - Anna',
      intimateDetail: 'Najpiękniejsza praktyka przy świecy',
      available: true,
      status: 'filling-fast',
      highlights: ['Taniec z żółwiami morskimi', 'Medytacje na białym piasku', 'Rytuały wschodu słońca', 'Cisza bez granic'],
      accommodation: 'Bungalowy nad oceanem',
      meals: 'Dary oceanu i tropików',
      activities: ['Nurkowanie z koralowcami', 'Joga o zachodzie', 'Wędrówki rowerowe', 'Głębokie oddychanie', 'Tradycyjne łodzie'],
      imageUrl: '/images/gallery/gili-air-beach.webp',
      culturalElements: ['Sasak tradycje', 'Lombok kultura', 'Życie bez samochodów']
    },
    {
      id: 'uluwatu-november-2025',
      type: 'Klify i Świątynie',
      title: 'ULUWATU',
      startDate: '15 listopada 2025',
      endDate: '25 listopada 2025',
      location: 'Uluwatu, Bali',
      participants: 8,
      maxParticipants: 14,
      price: '',
      description: 'Klify. Niebo. Wieczność.',
      testimonial: '"Transformacja na krawędzi świata" - Ola',
      intimateDetail: 'Tu praktykujemy z falami',
      available: true,
      status: 'confirmed',
      highlights: ['Wschody słońca nad oceanem', 'Święte świątynie na klifach', 'Taniec ognia Kecak', 'Harmonia z falami'],
      accommodation: 'Dom na klifach',
      meals: 'Czysta energia natury',
      activities: ['Joga na krawędzi świata', 'Pielgrzymki do świątyń', 'Taniec duchów o zachodzie', 'Surfowanie z falami', 'Rytuały uzdrowienia'],
      imageUrl: '/images/gallery/uluwatu-temple.webp',
      culturalElements: ['Pura Luhur Uluwatu', 'Taniec Kecak', 'Balijskie ceremonie']
    },
    {
      id: 'sri-lanka-october-2024',
      type: 'Ayurveda & Mindfulness',
      title: 'Perła Sri Lanka',
      startDate: '15 października 2024',
      endDate: '23 października 2024',
      location: 'Ella, Sri Lanka',
      participants: 0,
      maxParticipants: 10,
      price: '€2,800',
      description: 'Nowy wymiar duchowości w perle Oceanu Indyjskiego. Odkryj starożytną mądrość Ajurwedy i buddyjskich praktyk.',
      available: true,
      status: 'new',
      highlights: ['Authentic Ayurveda', 'Buddhist meditation', 'Tea plantation visits'],
      accommodation: 'Boutique mountain resort',
      meals: 'Traditional Sri Lankan & Ayurvedic',
      activities: ['Terapie ajurwedyjskie', 'Wizyty w świątyniach', 'Sanktuarium słoni', 'Podróże koleją']
    }
  ], []);



  return (
    <div className="wellness-page bg-sanctuary">
      <EnterpriseHeroSection />

      {/* =============================================
          ENTERPRISE 11/10 DESTINATIONS SECTION - SACRED SHOWCASE
          Museum-quality presentation of transformational journeys
          ============================================= */}
      <section id="destinations" className="container">
        <div className="text-center mb-20">
          <div className="section-divider mb-12"></div>
          <h2 className="section-header mb-8">
            Miejsca, gdzie oddychasz
          </h2>
          <p className="body-text max-w-3xl mx-auto mb-12 opacity-80">
            Twoja podróż zaczyna się tutaj. Odkryj magię Bali i Sri Lanka wraz z jogą, 
            która łączy cię z twoją prawdziwą naturą.
          </p>
          
          {/* Sacred Trinity - Cultural markers */}
          <div className="flex flex-col md:flex-row items-center justify-center gap-6 md:gap-12 text-stone text-sm font-light tracking-[0.15em] uppercase">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-temple-gold rounded-full opacity-60"></span>
              <span>Bali - Wyspa Bogów</span>
            </div>
            <div className="w-px h-4 bg-stone/20 hidden md:block"></div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-sage-green rounded-full opacity-60"></span>
              <span>Sri Lanka - Perła Oceanu</span>
            </div>
            <div className="w-px h-4 bg-stone/20 hidden md:block"></div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-ocean-blue rounded-full opacity-60"></span>
              <span>Duchowa Przemiana</span>
            </div>
          </div>
        </div>

        {/* SACRED GALLERY - Featured Destinations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 mb-24">
          {baliSriLankaRetreats.slice(0, 4).map((retreat) => (
            <EnterpriseRetreatCard
              key={retreat.id}
              title={retreat.title}
              description={retreat.description}
              imageUrl={retreat.imageUrl}
              link={`/program`}
              location={retreat.location}
              duration={retreat.duration}
              badge={retreat.status}
              testimonial={retreat.testimonial}
              intimateDetail={retreat.intimateDetail}
            />
          ))}
        </div>

        {/* SACRED INVITATION - Call to action */}
        <div className="text-center space-y-8">
          <div className="flex items-center justify-center my-12">
            <div className="flex items-center gap-4 text-temple-gold/60">
              <div className="w-12 h-px bg-temple-gold/30"></div>
              <span className="text-xl opacity-60">ॐ</span>
              <div className="w-12 h-px bg-temple-gold/30"></div>
            </div>
          </div>
          
          <div className="space-y-4">
            <p className="text-lg text-charcoal/70 font-light italic tracking-wide">
              "Każda podróż zaczyna się od jednego kroku..."
            </p>
            <p className="text-sm text-temple-gold font-light tracking-[0.1em] uppercase">
              Om Swastiastu - Niech pokój będzie z Tobą
            </p>
          </div>
          
          <Link
            href="/program"
            className="btn-ghost btn-primary"
          >
            Odkryj wszystkie programy
          </Link>
        </div>
      </section>



      {/* About Julia - Enterprise Authenticity */}
      <section className="container">
        <div className="section-divider mb-20"></div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center max-w-6xl mx-auto">
          {/* Content */}
          <div className="space-y-8">
            <div>
              <h2 className="section-header mb-8">
                Julia
              </h2>
              <p className="body-text italic text-temple-gold/80 text-lg mb-8">
                "Praktyka zaczyna się, gdy schodzimy z maty"
              </p>
            </div>

            {/* Personal Story - Not Business */}
            <div className="space-y-6">
              <p className="body-text">
                Moja droga z jogą zaczęła się nie w studiu, ale w momencie, gdy zrozumiałam,
                że prawdziwa transformacja dzieje się w codzienności. W sposobie, w jaki oddychamy
                w korku, jak reagujemy na trudności, jak traktujemy siebie i innych.
              </p>

              <p className="body-text">
                <span className="text-temple-gold">Bali i Sri Lanka nauczyły mnie ciszy</span> - tej prawdziwej,
                która nie oznacza braku dźwięku, ale spokój serca. Każda podróż to powrót do siebie.
              </p>

              <p className="body-text">
                Jako fizjoterapeutka z 8-letnim doświadczeniem i certyfikowana instruktorka jogi (RYT 500),
                łączę wiedzę medyczną z duchową praktyką, tworząc bezpieczną przestrzeń dla transformacji.
              </p>
            </div>

            {/* Spiritual Statistics - Humanized */}
            <div className="flex items-center justify-center gap-12 py-8">
              <div className="text-center">
                <div className="text-2xl text-temple-gold mb-2">∞</div>
                <div className="text-sm text-stone font-light">Wspólnych chwil</div>
              </div>
              <div className="text-center">
                <div className="text-2xl text-temple-gold mb-2">♡</div>
                <div className="text-sm text-stone font-light">Otwartych serc</div>
              </div>
              <div className="text-center">
                <div className="text-2xl text-temple-gold mb-2">ॐ</div>
                <div className="text-sm text-stone font-light">Wspólnych dróg</div>
              </div>
            </div>

            {/* Sacred Places */}
            <div className="text-center text-stone/60 text-sm font-light tracking-wide">
              Rishikesh • Ubud • Kandy • Gili Air • Uluwatu
            </div>

            {/* CTA to O Mnie */}
            <div className="text-center">
              <Link
                href="/o-mnie"
                className="btn-ghost"
              >
                Poznaj mnie bliżej
              </Link>
            </div>
          </div>

          {/* Image */}
          <div className="relative">
            <div className="aspect-[4/5] relative overflow-hidden">
              <Image
                src="/images/profile/omnie-opt.webp"
                alt="Julia Jakubowicz - Duchowa przewodniczka"
                fill
                className="object-cover filter sepia-[0.1] contrast-[0.95]"
                sizes="(max-width: 1024px) 100vw, 50vw"
                quality={95}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="container">
        <div className="section-divider mb-20"></div>
        
        <div className="text-center mb-16">
          <h2 className="section-header mb-8">
            Głosy serc
          </h2>
          <p className="body-text max-w-2xl mx-auto opacity-80">
            Historie transformacji naszych uczestniczek
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <TestimonialSlider testimonials={testimonials} />
        </div>
      </section>

      {/* Contact Section - Duchowa prostota */}
      <section className="container">
        <div className="section-divider mb-20"></div>
        
        <div className="text-center mb-16">
          <h2 className="section-header mb-8">
            Porozmawiajmy ♡
          </h2>
          <p className="body-text max-w-2xl mx-auto mb-8 opacity-80">
            Gotowa na transformację? Czekamy na Ciebie z otwartym sercem
          </p>
          <div className="text-stone/70 text-sm font-light tracking-wide">
            <span className="text-lg opacity-60">ॐ</span>
            <span className="mx-3">Każda podróż zaczyna się od pierwszego kroku</span>
            <span className="text-lg opacity-60">ॐ</span>
          </div>
        </div>

        {/* Uproszczone linki kontaktowe */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16 max-w-4xl mx-auto">
          <a
            href="https://www.instagram.com/fly_with_bakasana"
            target="_blank"
            rel="noopener noreferrer"
            className="text-center p-8 hover:opacity-70 transition-opacity duration-200"
          >
            <h3 className="font-light text-charcoal mb-2 tracking-wide">Instagram</h3>
            <p className="text-sm text-stone font-light">Codzienne inspiracje</p>
          </a>

          <a
            href="mailto:<EMAIL>"
            className="text-center p-8 hover:opacity-70 transition-opacity duration-200"
          >
            <h3 className="font-light text-charcoal mb-2 tracking-wide">Email</h3>
            <p className="text-sm text-stone font-light">Bezpośredni kontakt</p>
          </a>

          <a
            href="/kontakt"
            className="text-center p-8 hover:opacity-70 transition-opacity duration-200"
          >
            <h3 className="font-light text-charcoal mb-2 tracking-wide">Formularz</h3>
            <p className="text-sm text-stone font-light">Szczegółowe zapytanie</p>
          </a>
        </div>

        {/* WhatsApp CTA */}
        <div className="text-center">
          <a
            href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali i Sri Lanka. Czy możesz mi przesłać więcej informacji?"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-ghost btn-primary"
          >
            Napisz na WhatsApp
          </a>
        </div>
      </section>

      {/* Blog Section - Zapiski z Podróży */}
      <section className="container">
        <div className="section-divider mb-20"></div>
        
        <div className="text-center mb-16">
          <h2 className="section-header mb-8">
            Zapiski z Podróży
          </h2>
          <p className="body-text max-w-2xl mx-auto opacity-80">
            Historie napisane sercem, inspiracje z dalekich krajów
          </p>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 mb-16">
          {posts.map((post, index) => (
            <EnterpriseRetreatCard
              key={post.slug}
              title={transformTitleToPoetic(post.title)}
              description={post.excerpt}
              link={`/blog/${post.slug}`}
              imageUrl={post.imageUrl}
              className="blog-card"
            />
          ))}
        </div>

        <div className="text-center">
          <Link
            href="/blog"
            className="btn-ghost"
          >
            Wszystkie historie
          </Link>
        </div>
      </section>
      
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getOrganizationStructuredData())
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getYogaInstructorStructuredData())
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getFAQStructuredData(faqs))
        }}
      />
    </div>
  );
};

export default WellnessPage;