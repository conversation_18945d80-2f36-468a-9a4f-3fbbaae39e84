'use client';

import Link from 'next/link';
import Image from 'next/image';
import React, { useMemo } from 'react';

// BAKASANA Blog Card - Ultra-Minimalist Design
const PostCard = ({ post, featured = false, className = '' }) => {
  if (!post) return null;

  return (
    <article className={`blog-card ${className}`}>
      <Link href={`/blog/${post.slug || '#'}`} className="block">
        {/* SHARP EDITORIAL IMAGE - Zero border radius */}
        <div className="card-image" style={{
          backgroundImage: `url(${post.imageUrl || '/images/placeholder/image.jpg'})`,
          aspectRatio: '4/3',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          filter: 'sepia(0.2) contrast(0.9)',
          transition: 'filter 500ms ease',
          marginBottom: '32px'
        }}>
          {/* Hidden overlay with details - appears on hover */}
          <div className="card-overlay">
            <div className="subtle-text" style={{textAlign: 'center'}}>
              {post.category || 'Zapiski z podróży'}
            </div>
          </div>
        </div>

        {/* CONTENT - Pure Typography */}
        <div className="card-content" style={{padding: '0', textAlign: 'center'}}>
          {/* TITLE - Poetic transformation */}
          <h3 className="card-title" style={{
            fontSize: '24px',
            fontWeight: '300',
            lineHeight: '1.3',
            marginBottom: '24px'
          }}>
            {post.title || 'Bez tytułu'}
          </h3>

          {/* DESCRIPTION - Intimate excerpt */}
          <p className="card-description body-text" style={{
            fontSize: '13px',
            lineHeight: '1.6',
            maxWidth: 'none',
            opacity: '0',
            transition: 'opacity 300ms ease',
            marginBottom: '32px'
          }}>
            {post.excerpt || ''}
          </p>

          {/* SUBTLE LINK - Bottom right corner */}
          <div style={{position: 'relative'}}>
            <span className="card-details" style={{
              position: 'absolute',
              bottom: '0',
              right: '0',
              fontSize: '11px',
              fontWeight: '300',
              color: 'var(--stone)',
              textDecoration: 'none',
              letterSpacing: '0.5px',
              textTransform: 'uppercase',
              opacity: 'var(--opacity-whisper)',
              transition: 'opacity 300ms ease'
            }}>
              czytaj
            </span>
          </div>
        </div>
      </Link>
    </article>
  );
};

export default function BlogPageClientContent({ posts = [] }) {
  const memoizedPosts = useMemo(() => {
    if (!Array.isArray(posts)) return [];
    return posts.filter(post => post && typeof post === 'object');
  }, [posts]);

  return (
    <div className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Ultra-Minimal */}
      <section className="hero">
        <div className="hero-content">
          <h1 className="hero-title">
            ZAPISKI
          </h1>

          <p className="hero-subtitle">
            Historie napisane sercem
          </p>

          <div className="hero-meta">
            Bali • Sri Lanka • Wewnętrzne podróże
          </div>
        </div>
      </section>

      {/* BLOG POSTS - Asymmetric Poetry Layout */}
      <section className="container">
        <div className="text-center mb-16">
          <div className="section-divider"></div>
        </div>

        {/* Asymmetric 3-column layout with different heights */}
        <div className="blog-asymmetric-grid">
          {memoizedPosts.length > 0 ? (
            memoizedPosts.map((post, index) => (
              <PostCard
                key={`post-${post?.slug || index}`}
                post={post}
                className={`blog-card-${index + 1}`}
              />
            ))
          ) : (
            <div className="col-span-full text-center" style={{padding: '120px 0'}}>
              <div className="max-w-md mx-auto">
                <h3 className="section-header" style={{marginBottom: '16px'}}>Wkrótce więcej treści</h3>
                <p className="body-text">Pracujemy nad nowymi inspirującymi artykułami</p>
              </div>
            </div>
          )}
        </div>

        {/* READ MORE LINK - Subtle */}
        {memoizedPosts.length > 0 && (
          <div className="text-center" style={{marginTop: '80px'}}>
            <div className="section-divider"></div>
          </div>
        )}
      </section>

      {/* COMMUNITY SECTION - BAKASANA Standards */}
      <section className="container">
        <div className="text-center space-y-8 max-w-3xl mx-auto">
          <div className="space-y-6">
            <h3 className="section-header">
              Bądź na bieżąco
            </h3>

            <p className="body-text opacity-80">
              Otrzymuj najnowsze artykuły i inspiracje z duchowych podróży
            </p>
          </div>

          {/* SACRED DIVIDER */}
          <div className="flex items-center justify-center my-12">
            <div className="flex items-center gap-4 text-temple-gold/60">
              <div className="w-12 h-px bg-temple-gold/30"></div>
              <span className="text-lg opacity-60">ॐ</span>
              <div className="w-12 h-px bg-temple-gold/30"></div>
            </div>
          </div>

          {/* CONTACT LINKS - Ghost buttons */}
          <div className="flex flex-col sm:flex-row gap-8 justify-center items-center">
            <a
              href="https://www.instagram.com/fly_with_bakasana"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-ghost"
            >
              Instagram
            </a>
            <a
              href="mailto:<EMAIL>"
              className="btn-ghost"
            >
              Email
            </a>
          </div>

          <div className="pt-8">
            <p className="text-sm text-stone font-light italic tracking-wide">
              "Każda historia ma swoją moc..."
            </p>
            <p className="text-xs text-temple-gold font-light tracking-wide uppercase mt-2">
              Om Swastiastu
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}