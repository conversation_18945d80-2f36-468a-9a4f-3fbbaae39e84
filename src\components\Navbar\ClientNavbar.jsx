'use client';

import { useState, useEffect } from 'react';
import { mainNavItems } from '@/data/navigationLinks';

export default function ClientNavbar() {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav className={`navigation ${scrolled ? 'scrolled' : ''}`}>
      <div className="nav-container">
        {/* Logo BAKASANA - Precise enterprise specifications */}
        <a
          href="/"
          className="logo"
        >
          BAKASANA
        </a>

        {/* Navigation Links - Enterprise specifications */}
        <ul className="nav-links">
          {mainNavItems.slice(1).map(item => ( // Skip "Strona główna" for cleanliness
            <li key={item.href}>
              <a
                href={item.href}
                className="nav-link"
              >
                {item.label}
              </a>
            </li>
          ))}
        </ul>
      </div>
    </nav>
  );
}