'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { mainNavItems } from '@/data/navigationLinks';

export default function ClientNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (mobileMenuOpen && !event.target.closest('.navigation')) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [mobileMenuOpen]);

  // Prevent scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [mobileMenuOpen]);

  return (
    <nav
      id="navigation"
      className={`navigation ${scrolled ? 'scrolled' : ''}`}
      role="navigation"
      aria-label="Główna nawigacja"
    >
      <div className="nav-container">
        {/* Logo BAKASANA - Enterprise accessibility */}
        <Link
          href="/"
          className="logo"
          aria-label="BAKASANA - Strona główna"
        >
          BAKASANA
        </Link>

        {/* Desktop Navigation Links */}
        <ul className="nav-links hidden md:flex" role="menubar">
          {mainNavItems.slice(1).map(item => (
            <li key={item.href} role="none">
              <Link
                href={item.href}
                className="nav-link"
                role="menuitem"
                aria-label={`Przejdź do ${item.label}`}
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ul>

        {/* Mobile Menu Button */}
        <button
          className="mobile-menu-button md:hidden"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          aria-expanded={mobileMenuOpen}
          aria-controls="mobile-menu"
          aria-label={mobileMenuOpen ? 'Zamknij menu' : 'Otwórz menu'}
        >
          <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
        </button>

        {/* Mobile Menu */}
        <div
          id="mobile-menu"
          className={`mobile-menu ${mobileMenuOpen ? 'open' : ''}`}
          role="menu"
          aria-hidden={!mobileMenuOpen}
        >
          <ul className="mobile-nav-links" role="none">
            {mainNavItems.map(item => (
              <li key={item.href} role="none">
                <Link
                  href={item.href}
                  className="mobile-nav-link"
                  role="menuitem"
                  onClick={() => setMobileMenuOpen(false)}
                  aria-label={`Przejdź do ${item.label}`}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </nav>
  );
}