'use client';

import React from 'react';

// Enterprise Quality Badge - Discreet excellence marker
const EnterpriseQualityBadge = ({ position = 'bottom-right' }) => {
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50 opacity-60 hover:opacity-100 transition-opacity duration-300`}>
      <div className="flex items-center gap-2 px-3 py-1 bg-white/90 backdrop-blur-sm border border-sage/20 text-xs">
        <span className="text-sage font-light tracking-wider">ENTERPRISE</span>
        <div className="w-px h-3 bg-sage/40"></div>
        <span className="text-temple font-light tracking-wider">11/10</span>
        <div className="w-1 h-1 bg-temple rounded-full ml-1"></div>
      </div>
    </div>
  );
};

export default EnterpriseQualityBadge;