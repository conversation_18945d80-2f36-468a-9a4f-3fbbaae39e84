'use client';

import React from 'react';
import Link from 'next/link';

export default function Footer() {
  // Essential navigation links only (3 maximum)
  const essentialLinks = [
    { href: '/program', label: 'Bali' },
    { href: '/program/srilanka', label: 'Sri Lanka' },
    { href: '/kontakt', label: 'Konta<PERSON>' }
  ];

  return (
    <footer className="footer">
      <div className="footer-content">
        {/* Spiritual Greeting - More Prominent */}
        <div className="spiritual-greeting" style={{
          fontSize: '22px',
          color: 'var(--temple-gold)',
          opacity: '0.8',
          marginBottom: '32px'
        }}>
          ॐ Om Swastiastu ॐ
        </div>

        {/* Logo and Description */}
        <div style={{marginBottom: '48px'}}>
          <div className="logo" style={{marginBottom: '16px'}}>
            BAKASANA
          </div>
          <div className="subtle-text">
            Retreaty Jogi • Bali & Sri Lanka
          </div>
        </div>

        {/* Essential Footer Links Only */}
        <nav className="footer-links">
          {essentialLinks.map((link) => (
            <Link key={link.href} href={link.href} className="footer-link">
              {link.label}
            </Link>
          ))}
        </nav>

        {/* Social Links */}
        <div className="social-links">
          <a
            href="https://www.instagram.com/fly_with_bakasana"
            target="_blank"
            rel="noopener noreferrer"
            className="social-icon"
            aria-label="Instagram"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </a>
          <a
            href="mailto:<EMAIL>"
            className="social-icon"
            aria-label="Email"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
            </svg>
          </a>
        </div>

        {/* Spiritual Copyright */}
        <div className="copyright" style={{
          fontSize: '8px',
          opacity: '0.3',
          fontWeight: '300'
        }}>
          Made with ♡ for spiritual journeys
        </div>
      </div>
    </footer>
  );
}