@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== BAKASANA OPTIMIZED CSS - ONLY USED STYLES ===== */

/* ===== ACCESSIBILITY - SKIP LINKS ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 500;
  border-radius: 0;
  z-index: 1000;
  transition: top 300ms ease;
}

.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --sanctuary: #FDF9F3;
  --charcoal: #3A3A3A;
  --stone: #A8A8A8;
  --temple-gold: #C9A575;
  --sage-green: #7C9885;
  --ocean-blue: #4A6B7C;
  --glass-nav: rgba(253, 252, 248, 0.95);
  
  /* Typography */
  --font-primary: 'Cormorant Garamond', serif;
  --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  
  /* Layout */
  --container-max: 1200px;
  --section-padding: 120px 0;
  --element-breathing: 8%;
}

/* ===== GLOBAL RULES ===== */
* {
  box-sizing: border-box;
  border-radius: 0 !important; /* BAKASANA RULE: Zero border-radius */
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-secondary);
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--sanctuary);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
.section-header {
  font-family: var(--font-primary);
  font-size: clamp(32px, 5vw, 48px);
  font-weight: 300;
  letter-spacing: 0.1em;
  color: var(--charcoal);
  line-height: 1.2;
}

.body-text {
  font-size: 16px;
  line-height: 1.8;
  color: var(--charcoal);
  font-weight: 300;
}

/* ===== NAVIGATION ===== */
.navigation {
  position: fixed;
  top: 0;
  width: 100%;
  padding: 32px 8%;
  z-index: 100;
  background: transparent;
  transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
}

.navigation.scrolled {
  background: var(--glass-nav);
  backdrop-filter: blur(20px);
  padding: 20px 8%;
}

.nav-container {
  max-width: var(--container-max);
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-family: var(--font-primary);
  font-size: 22px;
  font-weight: 300;
  letter-spacing: 0.1em;
  color: var(--charcoal);
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 48px;
  list-style: none;
}

.nav-link {
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 300;
  letter-spacing: 0.05em;
  color: var(--charcoal);
  text-decoration: none;
  transition: opacity 300ms ease;
}

.nav-link:hover {
  opacity: 0.6;
}

/* ===== MOBILE NAVIGATION ===== */
.mobile-menu-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  transition: opacity 300ms ease;
}

.mobile-menu-button:hover {
  opacity: 0.7;
}

.mobile-menu-button:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

.hamburger-line {
  width: 20px;
  height: 1px;
  background: var(--charcoal);
  margin: 2px 0;
  transition: all 300ms ease;
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(3px, 3px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(3px, -3px);
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--sanctuary);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.open {
  opacity: 1;
  visibility: visible;
}

.mobile-nav-links {
  list-style: none;
  text-align: center;
  padding: 0;
  margin: 0;
}

.mobile-nav-link {
  display: block;
  font-family: var(--font-primary);
  font-size: 32px;
  font-weight: 300;
  letter-spacing: 0.1em;
  color: var(--charcoal);
  text-decoration: none;
  padding: 24px 0;
  transition: opacity 300ms ease;
}

.mobile-nav-link:hover,
.mobile-nav-link:focus {
  opacity: 0.6;
}

.mobile-nav-link:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 4px;
}

/* ===== LAYOUT ===== */
.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 120px 8%;
}

.section {
  padding: var(--section-padding);
}

/* ===== HERO SECTION ===== */
.hero {
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.hero-content {
  text-align: center;
  z-index: 2;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-family: var(--font-primary);
  font-size: clamp(72px, 10vw, 140px);
  font-weight: 200;
  letter-spacing: 0.25em;
  color: white;
  margin-bottom: 32px;
  opacity: 0.85;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-family: var(--font-secondary);
  font-size: clamp(16px, 2vw, 20px);
  font-weight: 300;
  letter-spacing: 0.1em;
  color: white;
  margin-bottom: 48px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-meta {
  font-size: 14px;
  color: white;
  opacity: 0.8;
  margin-bottom: 48px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-cta-link {
  color: white;
  text-decoration: none;
  font-weight: 300;
  letter-spacing: 0.1em;
  border-bottom: 1px solid transparent;
  transition: opacity 300ms ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-cta-link:hover {
  opacity: 1;
}

/* ===== BUTTONS ===== */
.btn-ghost {
  padding: 16px 48px;
  border: 1px solid var(--stone);
  background: transparent;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  text-decoration: none;
  display: inline-block;
  transition: all 300ms ease;
  cursor: pointer;
}

.btn-ghost:hover {
  opacity: 0.7;
}

.btn-primary {
  color: var(--temple-gold);
  border-color: var(--temple-gold);
}

.btn-primary:hover {
  opacity: 0.7;
}

/* ===== SECTION DIVIDER ===== */
.section-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--stone) 30%, var(--temple-gold) 50%, var(--stone) 70%, transparent 100%);
  opacity: 0.2;
  margin: 0 auto;
  max-width: 200px;
}

/* ===== BLOG CARDS ===== */
.blog-card {
  transition: opacity 300ms ease;
}

.blog-card:hover {
  opacity: 0.9;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 767px) {
  .nav-links { display: none; }
  .mobile-menu-button { display: flex; }
  .hero-title { font-size: 48px; letter-spacing: 0.15em; }
  .container { padding: 80px 5%; }
  .section { padding: 80px 0; }
}

@media (min-width: 768px) {
  .mobile-menu-button { display: none; }
  .mobile-menu { display: none; }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== GLOBAL LINKS ===== */
a {
  color: inherit;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

a:hover {
  opacity: 0.7;
}

/* ===== IMAGES ===== */
img {
  max-width: 100%;
  height: auto;
  display: block;
  width: 100%;
  filter: sepia(0.05) contrast(0.95);
  transition: filter 500ms ease;
}

img:hover {
  opacity: 0.9;
}
