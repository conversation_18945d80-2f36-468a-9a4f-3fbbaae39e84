'use client';

import React from 'react';
import Link from 'next/link';

const QuickCTA = () => {
  return (
    <div className="fixed top-1/2 right-6 transform -translate-y-1/2 z-40">
      {/* Ultra-Subtle Reminder */}
      <div style={{
        background: 'var(--sanctuary)',
        border: '1px solid var(--stone)',
        borderOpacity: '0.3',
        padding: '20px',
        maxWidth: '240px',
        opacity: '0.9'
      }}>
        <div className="text-center">
          <h3 className="card-title" style={{marginBottom: '12px', fontSize: '16px', fontWeight: '300'}}>
            Gotowa?
          </h3>
          <p className="body-text" style={{marginBottom: '20px', fontSize: '12px', opacity: '0.7'}}>
            Następna podróż zaczyna się jutro
          </p>

          <Link
            href="/kontakt"
            className="btn-ghost btn-primary"
            style={{
              width: '100%',
              textAlign: 'center',
              padding: '12px 24px',
              fontSize: '11px',
              opacity: '0.8'
            }}
          >
            Kontakt
          </Link>
        </div>
      </div>
    </div>
  );
};

export default QuickCTA;