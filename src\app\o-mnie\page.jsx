import React from 'react';
import Image from 'next/image';
import { generateMetadata as generateSEOMetadata, generateStructuredData } from '../metadata';

export const metadata = generateSEOMetadata({
  title: '<PERSON> - Instruktorka Jogi i Fizjoterapeutka',
  description: 'Poznaj Jul<PERSON> - magistra fizjoterapii z 8-letnim doświadczeniem i certyfikowaną instruktorkę jogi RYT 500. Organizatorka retreatów jogowych na Bali od 2020 roku.',
  keywords: [
    '<PERSON>',
    'instruktorka jogi RYT 500',
    'fizjoterapeutka magister',
    'joga terapeutyczna',
    'organizator retreatów Bali',
    'Yoga Alliance',
    'doświadczenie fizjoterapia',
    'holistyczne podejście joga'
  ],
});

export default function OMniePage() {
  const structuredData = generateStructuredData({
    type: 'Person',
    name: '<PERSON>',
    description: 'Magister fiz<PERSON><PERSON>pi<PERSON> i certyfikowana instruktorka jogi RYT 500. Organizatorka retreatów jogowych na Bali.',
  });

  const qualifications = [
    'Magister fizjoterapii z 8-letnim doświadczeniem klinicznym',
    'Certyfikowana instruktorka jogi (RYT 500) - Yoga Alliance',
    'Specjalizacja w jodze terapeutycznej i rehabilitacyjnej',
    'Ukończone kursy: Vinyasa, Hatha, Yin Yoga, Pranayama',
    'Organizator retreatów jogowych na Bali od 2020 roku',
    'Autorka programów łączących fizjoterapię z praktyką jogi',
    'Współpraca z ośrodkami rehabilitacyjnymi i studiami jogi',
    'Certyfikat w zakresie anatomii i biomechaniki ruchu'
  ];

  const achievements = [
    {
      title: 'Ponad 2000 godzin nauczania jogi',
      description: 'Doświadczenie w pracy z różnymi grupami wiekowymi i poziomami zaawansowania',
      category: 'Doświadczenie'
    },
    {
      title: 'Kilka udanych retreatów na Bali',
      description: 'Zorganizowane transformacyjne wyjazdy dla uczestników z całej Polski',
      category: 'Retreaty'
    },
    {
      title: 'Autorska metodyka łączenia fizjoterapii z jogą',
      description: 'Innowacyjne podejście do terapii bólu pleców i problemów postawy',
      category: 'Specjalizacja'
    },
    {
      title: 'Współpraca z ekspertami',
      description: 'Stała współpraca z lekarzami ortopedami i fizjoterapeutami',
      category: 'Partnerstwa'
    }
  ];

  const philosophy = [
    {
      title: 'Holistyczne podejście',
      description: 'Łączę wiedzę medyczną z duchową praktyką jogi, tworząc kompleksowe programy zdrowotne.',
      category: 'Metodyka'
    },
    {
      title: 'Indywidualizacja',
      description: 'Każdy uczestnik otrzymuje personalne wskazówki dostosowane do jego potrzeb i możliwości.',
      category: 'Podejście'
    },
    {
      title: 'Bezpieczeństwo przede wszystkim',
      description: 'Moje doświadczenie fizjoterapeutyczne gwarantuje bezpieczną praktykę dla wszystkich.',
      category: 'Priorytet'
    },
    {
      title: 'Transformacja przez podróż',
      description: 'Wierzę, że połączenie jogi z magią Bali tworzy przestrzeń dla głębokiej przemiany.',
      category: 'Wizja'
    }
  ];

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className="bg-sanctuary min-h-screen">
        {/* HERO SECTION - Ultra-Minimal */}
        <section className="hero">
          <div className="hero-content">
            <h1 className="hero-title">
              JULIA
            </h1>

            <p className="hero-subtitle">
              Przewodniczka duchowa na ścieżce jogi
            </p>

            <div className="hero-meta">
              Fizjoterapia • Joga RYT 500 • Transformacja
            </div>

            <div className="hero-cta">
              <a 
                href="#about-julia" 
                className="hero-cta-link"
              >
                Poznaj moją historię
              </a>
            </div>
          </div>
        </section>

        {/* MAIN PROFILE SECTION - BAKASANA Standards */}
        <section id="about-julia" className="container">
          <div className="text-center mb-20">
            <div className="section-divider mb-12"></div>
            <h2 className="section-header mb-8">
              Moja droga do jogi
            </h2>
            <p className="body-text max-w-3xl mx-auto opacity-80">
              Historia fizjoterapeutki, która znalazła w jodze sposób na holistyczne leczenie
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center max-w-6xl mx-auto">
            {/* Profile Content */}
            <div className="space-y-8">
              {/* SACRED QUOTE */}
              <div className="text-center lg:text-left">
                <p className="text-xl text-temple-gold/80 font-light italic tracking-wide mb-8">
                  "Praktyka zaczyna się, gdy schodzimy z maty"
                </p>
              </div>

              <div className="space-y-6 body-text">
                <p>
                  Jestem magistrem fizjoterapii z 8-letnim doświadczeniem klinicznym oraz certyfikowaną
                  instruktorką jogi (RYT 500). Moją pasją jest łączenie wiedzy medycznej z duchową
                  praktyką jogi, tworząc holistyczne podejście do zdrowia i dobrostanu.
                </p>

                <p>
                  <span className="text-temple-gold">Bali i Sri Lanka stały się moim drugim domem</span> - miejscami, 
                  gdzie odkryłam, że prawdziwe leczenie to nie tylko naprawa ciała, ale odnalezienie 
                  równowagi między ciałem, umysłem i duszą.
                </p>

                <p>
                  Organizuję retreaty jogowe, które łączą tradycyjną praktykę z nowoczesną wiedzą
                  o anatomii i biomechanice. W Polsce prowadzę 
                  <a href="https://flywithbakasana.pl/" target="_blank" rel="noopener noreferrer" className="text-temple-gold hover:opacity-70 transition-opacity"> Studio Bakasana w Rzeszowie</a>,
                  gdzie uczę różnych stylów jogi dla wszystkich poziomów zaawansowania.
                </p>

                <p>
                  Specjalizuję się w jodze terapeutycznej, pomagając osobom z problemami kręgosłupa
                  i zaburzeniami postawy. Wierzę, że każdy może praktykować jogę - niezależnie od 
                  wieku, kondycji czy doświadczenia.
                </p>
              </div>

              {/* Spiritual Statistics */}
              <div className="flex items-center justify-center lg:justify-start gap-12 py-8">
                <div className="text-center">
                  <div className="text-2xl text-temple-gold mb-2">8+</div>
                  <div className="text-sm text-stone font-light">Lat doświadczenia</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl text-temple-gold mb-2">RYT 500</div>
                  <div className="text-sm text-stone font-light">Certyfikacja</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl text-temple-gold mb-2">∞</div>
                  <div className="text-sm text-stone font-light">Transformacji</div>
                </div>
              </div>
            </div>

            {/* Profile Image */}
            <div className="relative">
              <div className="aspect-[4/5] relative overflow-hidden">
                <Image
                  src="/images/profile/omnie-opt.webp"
                  alt="Julia Jakubowicz - Instruktorka jogi i fizjoterapeutka"
                  fill
                  priority
                  className="object-cover filter sepia-[0.1] contrast-[0.95]"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
            </div>
          </div>
        </section>

        {/* SECTION DIVIDER */}
        <div className="section-divider my-24"></div>

        {/* KWALIFIKACJE - Ultra-minimal */}
        <section className="container">
          <div className="text-center mb-16">
            <h3 className="section-header mb-6">
              Kwalifikacje i Doświadczenie
            </h3>
            <p className="body-text opacity-80 max-w-2xl mx-auto">
              Profesjonalne wykształcenie i certyfikaty
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {qualifications.map((qualification, index) => (
                <div key={index} className="flex items-start gap-4 py-4 border-b border-stone/20 last:border-b-0">
                  <div className="w-2 h-2 bg-temple-gold/60 mt-2 flex-shrink-0"></div>
                  <span className="body-text">
                    {qualification}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* SECTION DIVIDER */}
        <div className="section-divider my-24"></div>

        {/* OSIĄGNIĘCIA - Ultra-minimal */}
        <section className="container">
          <div className="text-center mb-16">
            <h3 className="section-header mb-6">
              Profesjonalne Osiągnięcia
            </h3>
            <p className="body-text opacity-80 max-w-2xl mx-auto">
              Moje najważniejsze sukcesy zawodowe
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-5xl mx-auto">
            {achievements.map((achievement, index) => (
              <div key={index} className="space-y-4 text-center md:text-left">
                <h4 className="text-xl font-light text-charcoal tracking-wide font-serif">
                  {achievement.title}
                </h4>
                <p className="body-text opacity-80">
                  {achievement.description}
                </p>
              </div>
            ))}
          </div>
        </section>

        {/* SECTION DIVIDER */}
        <div className="section-divider my-24"></div>

        {/* FILOZOFIA - Ultra-minimal */}
        <section className="container">
          <div className="text-center mb-16">
            <h3 className="section-header mb-6">
              Filozofia Pracy
            </h3>
            <p className="body-text opacity-80 max-w-2xl mx-auto">
              Wartości które kierują moją praktyką
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-5xl mx-auto">
            {philosophy.map((item, index) => (
              <div key={index} className="space-y-4 text-center md:text-left">
                <h4 className="text-xl font-light text-charcoal tracking-wide font-serif">
                  {item.title}
                </h4>
                <p className="body-text opacity-80">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </section>

        {/* SECTION DIVIDER */}
        <div className="section-divider my-24"></div>

        {/* CALL TO ACTION - Ultra-minimal */}
        <section className="container">
          <div className="text-center space-y-8 max-w-4xl mx-auto">
            <div className="space-y-6">
              <h3 className="section-header">
                Gotowa na transformację?
              </h3>

              <p className="body-text opacity-80">
                Dołącz do mnie na Bali lub Sri Lanka i odkryj, jak joga może zmienić Twoje życie.
                Każdy retreat to unikalna podróż łącząca praktykę z magią Azji.
              </p>
            </div>

            {/* SACRED DIVIDER */}
            <div className="flex items-center justify-center my-12">
              <div className="flex items-center gap-4 text-temple-gold/60">
                <div className="w-12 h-px bg-temple-gold/30"></div>
                <span className="text-lg opacity-60">ॐ</span>
                <div className="w-12 h-px bg-temple-gold/30"></div>
              </div>
            </div>

            {/* Destinations highlights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              <div className="text-center p-6">
                <h4 className="text-temple-gold font-light mb-2">Bali - Wyspa Bogów</h4>
                <p className="text-sm text-stone font-light">Ubud • Canggu • Gili Air • Uluwatu</p>
              </div>
              <div className="text-center p-6">
                <h4 className="text-sage-green font-light mb-2">Sri Lanka - Perła Oceanu</h4>
                <p className="text-sm text-stone font-light">Ella • Kandy • Ayurveda • Świątynie</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-8 justify-center items-center">
              <a
                href="/program"
                className="btn-ghost btn-primary"
                aria-label="Zobacz programy retreatów jogowych na Bali i Sri Lanka"
              >
                Zobacz Programy Retreatów
              </a>

              <span className="text-stone text-sm font-light">lub</span>

              <a
                href="/kontakt"
                className="btn-ghost"
                aria-label="Skontaktuj się z Julią"
              >
                Skontaktuj się ze mną
              </a>
            </div>

            <div className="pt-8">
              <p className="text-sm text-stone font-light italic tracking-wide">
                "Każda podróż zaczyna się od jednego kroku..."
              </p>
              <p className="text-xs text-temple-gold font-light tracking-wide uppercase mt-2">
                Om Swastiastu 🙏 Ayubowan
              </p>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}