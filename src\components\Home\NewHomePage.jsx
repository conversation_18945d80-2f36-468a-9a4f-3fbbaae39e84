'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

// 1. HERO SECTION
const HeroSection = () => {
  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden">
      {/* Hero Image */}
      <div className="absolute inset-0">
        <Image
          src="/images/background/bali-hero.webp"
          alt="Joga i odkrywanie z Julią"
          fill
          className="object-cover"
          sizes="100vw"
          priority
          quality={95}
        />
        <div className="absolute inset-0 bg-black/30"></div>
      </div>
      
      {/* Hero Content */}
      <div className="relative z-10 h-full flex items-center justify-center">
        <div className="text-center text-white px-6">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-light mb-6 font-serif">
            Joga i odkrywanie z Julią
          </h1>
          
          <Link href="/program">
            <button className="mt-8 px-8 py-3 bg-white text-stone-800 font-light tracking-wide uppercase text-sm hover:bg-stone-100 transition-colors duration-200">
              Zobacz najbliższe wyjazdy
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

// 2. KRÓTKO O WYJAZDACH - 3 kolumny
const DestinationsSection = () => {
  const destinations = [
    {
      id: 'bali',
      title: 'Bali',
      description: 'Odkryj magię ryżowych tarasów i świętych świątyń. Miejsce, gdzie duchowość spotyka się z naturą.',
      image: '/images/programs/program1.webp',
      link: '/program/bali'
    },
    {
      id: 'sri-lanka',
      title: 'Sri Lanka',
      description: 'Perła Oceanu Indyjskiego pełna ajurwedyjskich tradycji. Harmonia ciała, umysłu i duszy.',
      image: '/images/programs/program2.webp',
      link: '/program/srilanka'
    },
    {
      id: 'joga',
      title: 'Joga',
      description: 'Certyfikowana praktyka RYT 500. Połączenie tradycji z nowoczesnym podejściem do uzdrowienia.',
      image: '/images/profile/omnie-opt.webp',
      link: '/o-mnie',
      isYoga: true
    }
  ];

  return (
    <section className="py-20 px-6 max-w-6xl mx-auto">
      <div className="grid md:grid-cols-3 gap-12">
        {destinations.map((dest) => (
          <div key={dest.id} className="group">
            <div className="relative aspect-[4/3] mb-6 overflow-hidden">
              {dest.isYoga ? (
                <div className="h-full bg-stone-100 flex items-center justify-center">
                  <div className="w-16 h-16 bg-stone-300 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-stone-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                </div>
              ) : (
                <Image
                  src={dest.image}
                  alt={dest.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="(max-width: 768px) 100vw, 33vw"
                  quality={90}
                />
              )}
            </div>
            
            <h3 className="text-2xl font-light mb-4 font-serif">{dest.title}</h3>
            <p className="text-stone-600 mb-6 leading-relaxed">{dest.description}</p>
            
            <Link
              href={dest.link}
              className="inline-flex items-center text-sm font-light tracking-wide uppercase text-stone-800 hover:text-stone-600 transition-colors duration-200"
            >
              <span>{dest.isYoga ? 'O mojej praktyce' : 'Dowiedz się więcej'}</span>
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        ))}
      </div>
    </section>
  );
};

// 3. POZNAJ JULIĘ
const AboutSection = () => {
  return (
    <section className="py-20 px-6 max-w-6xl mx-auto">
      <div className="grid md:grid-cols-2 gap-12 items-center">
        <div className="relative aspect-[3/4] overflow-hidden">
          <Image
            src="/images/profile/omnie-opt.webp"
            alt="Julia Jakubowicz"
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, 50vw"
            quality={95}
          />
        </div>
        
        <div>
          <h2 className="text-3xl md:text-4xl font-light mb-6 font-serif">Poznaj Julię</h2>
          
          <div className="space-y-4 text-stone-700 leading-relaxed">
            <p>
              Jestem fizjoterapeutką z wykształcenia, instruktorką jogi z sercem. 
              Moja podróż z jogą rozpoczęła się wiele lat temu, gdy szukałam sposobu na 
              połączenie pracy z ciałem z duchowym rozwojem.
            </p>
            
            <p>
              Dzisiaj łączę wiedzę medyczną z mądrością starożytnych tradycji, 
              tworząc przestrzeń dla transformacji i uzdrowienia. Każdy wyjazd to 
              dla mnie możliwość dzielenia się tym, co najcenniejsze.
            </p>
          </div>
          
          <Link
            href="/o-mnie"
            className="inline-flex items-center mt-8 px-6 py-3 border border-stone-300 text-sm font-light tracking-wide uppercase hover:bg-stone-50 transition-colors duration-200"
          >
            <span>Przeczytaj całą historię</span>
          </Link>
        </div>
      </div>
    </section>
  );
};

// 4. TESTIMONIALE
const TestimonialsSection = () => {
  const testimonials = [
    {
      quote: "To była najbardziej transformująca podróż mojego życia. Julia stworzyła przestrzeń ciepła i bezpieczeństwa.",
      author: "Anna Kowalska",
      location: "Warszawa",
      avatar: "/images/profile/omnie-opt.webp"
    },
    {
      quote: "Idealny balans jogi, kultury i relaksu. Julia ma dar tworzenia magicznych chwil, które zostają w pamięci na zawsze.",
      author: "Katarzyna Nowak",
      location: "Gdańsk", 
      avatar: "/images/profile/omnie-opt.webp"
    },
    {
      quote: "Każdy dzień przynosił nowe odkrycia i głębsze zrozumienie siebie. To nie był tylko retreat - to była prawdziwa podróż.",
      author: "Marta Wiśniewska",
      location: "Wrocław",
      avatar: "/images/profile/omnie-opt.webp"
    }
  ];

  return (
    <section className="py-20 px-6 bg-stone-50">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-light mb-12 text-center font-serif">
          Co mówią uczestniczki
        </h2>
        
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white p-8 shadow-sm">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-stone-200 rounded-full overflow-hidden mr-4">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.author}
                    width={48}
                    height={48}
                    className="object-cover"
                  />
                </div>
                <div>
                  <div className="font-medium text-stone-900">{testimonial.author}</div>
                  <div className="text-sm text-stone-500">{testimonial.location}</div>
                </div>
              </div>
              
              <p className="text-stone-700 leading-relaxed italic">
                "{testimonial.quote}"
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// 5. NAJBLIŻSZY WYJAZD
const NextTripSection = () => {
  const nextTrip = {
    title: "Serce Dżungli",
    location: "Ubud, Bali",
    date: "15-27 marca 2025",
    price: "od 4990 zł",
    description: "Wiosenna transformacja wśród ryżowych tarasów i świętych świątyń.",
    features: [
      "12 dni pełnych praktyki",
      "Zakwaterowanie w sanktuarium",
      "Wszystkie wegetariańskie posiłki",
      "Transfery i wycieczki"
    ]
  };

  return (
    <section className="py-20 px-6 max-w-4xl mx-auto text-center">
      <h2 className="text-3xl md:text-4xl font-light mb-8 font-serif">
        Najbliższy wyjazd
      </h2>
      
      <div className="bg-stone-50 p-8 md:p-12">
        <h3 className="text-2xl font-light mb-4 font-serif">{nextTrip.title}</h3>
        
        <div className="flex items-center justify-center gap-4 mb-6 text-stone-600">
          <span>{nextTrip.location}</span>
          <span>•</span>
          <span>{nextTrip.date}</span>
        </div>
        
        <p className="text-stone-700 mb-6 leading-relaxed max-w-2xl mx-auto">
          {nextTrip.description}
        </p>
        
        <div className="grid md:grid-cols-2 gap-4 mb-8 text-sm text-stone-600">
          {nextTrip.features.map((feature, index) => (
            <div key={index} className="flex items-center justify-center">
              <svg className="w-4 h-4 mr-2 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              {feature}
            </div>
          ))}
        </div>
        
        <div className="text-xl font-light mb-6">{nextTrip.price}</div>
        
        <Link href="/rezerwacja">
          <button className="px-8 py-3 bg-stone-800 text-white font-light tracking-wide uppercase text-sm hover:bg-stone-700 transition-colors duration-200">
            Zarezerwuj miejsce
          </button>
        </Link>
      </div>
    </section>
  );
};

// 6. KONTAKT
const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section className="py-20 px-6 bg-stone-100">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-light mb-12 text-center font-serif">
          Skontaktuj się ze mną
        </h2>
        
        <div className="grid md:grid-cols-2 gap-12">
          {/* Form */}
          <div>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-light mb-2">Imię</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-stone-300 focus:outline-none focus:border-stone-500 transition-colors duration-200"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-light mb-2">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-stone-300 focus:outline-none focus:border-stone-500 transition-colors duration-200"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-light mb-2">Wiadomość</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows="4"
                  className="w-full px-4 py-3 border border-stone-300 focus:outline-none focus:border-stone-500 transition-colors duration-200 resize-none"
                  required
                ></textarea>
              </div>
              
              <button
                type="submit"
                className="w-full px-6 py-3 bg-stone-800 text-white font-light tracking-wide uppercase text-sm hover:bg-stone-700 transition-colors duration-200"
              >
                Wyślij wiadomość
              </button>
            </form>
          </div>
          
          {/* Contact Info */}
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-light mb-4">Napisz do mnie</h3>
              <a
                href="mailto:<EMAIL>"
                className="text-stone-700 hover:text-stone-500 transition-colors duration-200"
              >
                <EMAIL>
              </a>
            </div>
            
            <div>
              <h3 className="text-lg font-light mb-4">Śledź mnie</h3>
              <a
                href="https://instagram.com/julia.yoga"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-stone-700 hover:text-stone-500 transition-colors duration-200"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
                @julia.yoga
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Main Component
const NewHomePage = () => {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <DestinationsSection />
      <AboutSection />
      <TestimonialsSection />
      <NextTripSection />
      <ContactSection />
    </div>
  );
};

export default NewHomePage;