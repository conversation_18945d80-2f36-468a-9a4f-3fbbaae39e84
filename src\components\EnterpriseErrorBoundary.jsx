'use client';

import React from 'react';

class EnterpriseErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Log to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      console.error('Enterprise Error Boundary caught an error:', error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="enterprise-error">
          <div className="error-content">
            {/* Sacred Symbol */}
            <div className="error-symbol">
              <div className="lotus-container">
                <div className="lotus-petals">
                  <span>❀</span>
                </div>
              </div>
            </div>

            {/* Spiritual Error Message */}
            <div className="error-message">
              <h1 className="section-header" style={{marginBottom: '24px'}}>
                Chwilowa Nieharmonia
              </h1>
              
              <p className="body-text" style={{marginBottom: '32px', maxWidth: '500px', margin: '0 auto 32px auto'}}>
                Jak w naturze, czasem potrzebujemy chwili, aby przywrócić równowagę. 
                Nasze cyfrowe sanktuarium przechodzi przez moment transformacji.
              </p>

              <div className="sacred-quote" style={{marginBottom: '48px'}}>
                "W każdym końcu kryje się nowy początek"
                <cite style={{display: 'block', marginTop: '16px', fontSize: '12px', opacity: '0.6'}}>
                  — Stara Balijaska Mądrość
                </cite>
              </div>

              {/* Action Buttons */}
              <div style={{display: 'flex', gap: '16px', justifyContent: 'center', flexWrap: 'wrap'}}>
                <button
                  onClick={() => window.location.reload()}
                  className="btn-ghost btn-primary"
                >
                  Odnów Energię
                </button>
                
                <a
                  href="/"
                  className="btn-ghost btn-accent"
                >
                  Powrót do Sanktuarium
                </a>
              </div>

              {/* Contact Information */}
              <div style={{marginTop: '48px', padding: '24px', background: 'var(--whisper)', textAlign: 'center'}}>
                <p className="subtle-text" style={{marginBottom: '16px'}}>
                  Jeśli problem się powtarza, skontaktuj się z nami
                </p>
                <div style={{display: 'flex', gap: '24px', justifyContent: 'center', flexWrap: 'wrap'}}>
                  <a
                    href="mailto:<EMAIL>"
                    className="subtle-text"
                    style={{textDecoration: 'none'}}
                  >
                    <EMAIL>
                  </a>
                  <a
                    href="https://wa.me/48123456789"
                    className="subtle-text"
                    style={{textDecoration: 'none'}}
                  >
                    WhatsApp
                  </a>
                </div>
              </div>

              {/* Development Error Details */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details style={{marginTop: '48px', textAlign: 'left', maxWidth: '600px', margin: '48px auto 0 auto'}}>
                  <summary style={{cursor: 'pointer', marginBottom: '16px', fontSize: '12px', color: 'var(--stone)'}}>
                    Szczegóły błędu (tylko w trybie deweloperskim)
                  </summary>
                  <pre style={{
                    background: '#f5f5f5',
                    padding: '16px',
                    fontSize: '11px',
                    overflow: 'auto',
                    maxHeight: '200px',
                    color: '#333'
                  }}>
                    {this.state.error && this.state.error.toString()}
                    {this.state.errorInfo.componentStack}
                  </pre>
                </details>
              )}
            </div>
          </div>

          <style jsx>{`
            .enterprise-error {
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
              background: var(--sanctuary);
              padding: 120px 8%;
            }

            .error-content {
              text-align: center;
              max-width: 800px;
              margin: 0 auto;
            }

            .error-symbol {
              margin-bottom: 48px;
            }

            .lotus-container {
              width: 80px;
              height: 80px;
              margin: 0 auto;
              display: flex;
              align-items: center;
              justify-content: center;
              border: 1px solid var(--temple-gold);
              border-radius: 50%;
              opacity: 0.6;
            }

            .lotus-petals {
              font-size: 24px;
              color: var(--temple-gold);
              animation: gentle-pulse 3s ease-in-out infinite;
            }

            @keyframes gentle-pulse {
              0%, 100% {
                opacity: 0.6;
                transform: scale(1);
              }
              50% {
                opacity: 1;
                transform: scale(1.05);
              }
            }

            @media (prefers-reduced-motion: reduce) {
              .lotus-petals {
                animation: none;
              }
            }

            @media (max-width: 768px) {
              .enterprise-error {
                padding: 80px 5%;
              }
              
              .error-content {
                max-width: 100%;
              }
            }
          `}</style>
        </div>
      );
    }

    return this.props.children;
  }
}

export default EnterpriseErrorBoundary;
