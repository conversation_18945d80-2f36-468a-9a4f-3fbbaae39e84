/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx}'],
  theme: {
    extend: {
      colors: {
        // =============================================
        // 🏛️ BAKASANA ULTRA-MINIMALIST COLOR SYSTEM
        // Aligned with CSS variables in globals.css
        // =============================================

        // CORE BAKASANA PALETTE - Matches CSS variables exactly
        sanctuary: '#FDFCF8',        // var(--sanctuary) - Mleczny główne tło
        charcoal: '#3A3A3A',         // var(--charcoal) - Ciepły antracyt tekst
        stone: '#A8A8A8',            // var(--stone) - Tekst drugorządny (updated to match CSS)
        'stone-light': '#C8C8C8',    // var(--stone-light) - Ultra-subtle elements
        whisper: '#F9F7F2',          // var(--whisper) - Footer tło
        rice: '#F5F3EF',             // var(--rice) - Alternating sections

        // SPIRITUAL ACCENTS - Matches CSS variables
        'temple-gold': '#C4A575',    // var(--temple-gold) - Delikatny złoty
        'sage-green': '#7C9885',     // var(--sage-green) - Tarasy ryżowe
        'ocean-blue': '#4A6B7C',     // var(--ocean-blue) - Sri Lanka
        'om-symbol': '#D4A574',      // var(--om-symbol) - Elementy duchowe
        'lotus-pink': '#E8D5D0',     // var(--lotus-pink) - Subtelne akcenty
        incense: '#A8A5A0',          // var(--incense) - Neutralne elementy

        // TRANSPARENCY SYSTEM - Matches CSS variables
        'glass-nav': 'rgba(253, 252, 248, 0.95)',  // var(--glass-nav)
        'subtle-shadow': 'rgba(0, 0, 0, 0.02)',    // var(--subtle-shadow)
        'hover-overlay': 'rgba(0, 0, 0, 0.1)',     // var(--hover-overlay)

        // ALIASES FOR CONSISTENCY - Use CSS variable names
        primary: '#3A3A3A',          // Alias for charcoal
        secondary: '#A8A8A8',        // Alias for stone
        accent: '#C4A575',           // Alias for temple-gold
        background: '#FDFCF8',       // Alias for sanctuary

        // DEPRECATED - For backward compatibility only
        temple: {
          DEFAULT: '#C4A575',        // Use temple-gold instead
          light: '#E6C78A',
          dark: '#A68B4B',
        },
        sage: {
          DEFAULT: '#7C9885',        // Use sage-green instead
          light: '#A8B5A8',
          dark: '#5A6B5D',
        },
        ocean: {
          DEFAULT: '#4A6B7C',        // Use ocean-blue instead
          light: '#7A9BAC',
        },
      },
      fontFamily: {
        // Duchowa typografia - Oddech w słowach
        'cormorant': ['Cormorant Garamond', 'Didot', 'Bodoni MT', 'Playfair Display', 'serif'],
        'inter': ['Inter', 'Helvetica Neue', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],

        // Zachowane dla kompatybilności
        'playfair': ['var(--font-playfair)', 'Playfair Display', 'serif'],
        'didot': ['Didot', 'Bodoni MT', 'Playfair Display', 'serif'],
        'helvetica': ['Helvetica Neue', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
      },
      fontSize: {
        'hero': ['4rem', { lineHeight: '1.1', letterSpacing: '0.2em' }],
        'display': ['2.5rem', { lineHeight: '1.2', letterSpacing: '0.02em' }],
        'heading': ['1.5rem', { lineHeight: '1.3', letterSpacing: '0.02em' }],
        'body': ['1rem', { lineHeight: '1.8', fontWeight: '300' }],
        'caption': ['0.875rem', { lineHeight: '1.6', letterSpacing: '0.05em' }],
        'micro': ['0.75rem', { lineHeight: '1.5', letterSpacing: '0.1em' }],
      },
      spacing: {
        'section': '120px',      // 120px między sekcjami minimum
        'container': '8%',       // 8% marginesy boczne
        'breathe': '180px',      // Hojne odstępy dla luksusowego odczucia
        'element': '2rem',       // Wewnętrzne paddingi elementów
        'card': '3rem',          // Paddingi kart
      },
      screens: {
        // BAKASANA ENTERPRISE BREAKPOINTS - Aligned with CSS
        'xs': '480px',           // Small mobile to mobile
        'sm': '768px',           // Mobile to tablet
        'md': '1024px',          // Tablet to desktop
        'lg': '1440px',          // Desktop to large
        'xl': '1920px',          // Large to ultra
      },
      container: {
        center: true,
        padding: '8%',           // 8% marginesy boczne - przestrzeń jako luksus
        screens: {
          'xs': '480px',
          'sm': '768px',
          'md': '1024px',
          'lg': '1440px',
          'xl': '1920px',
        },
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'lotus-pulse': 'lotusPulse 3s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(40px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        lotusPulse: {
          '0%, 100%': { opacity: '0.6', transform: 'scale(1)' },
          '50%': { opacity: '1', transform: 'scale(1.05)' },
        },
      },
    },
  },
  plugins: [],
}