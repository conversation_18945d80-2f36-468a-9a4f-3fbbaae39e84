'use client';

import React, { useState, useEffect } from 'react';

const EnterpriseLoader = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(() => {
            setIsVisible(false);
            onComplete?.();
          }, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 50);

    return () => clearInterval(timer);
  }, [onComplete]);

  if (!isVisible) return null;

  return (
    <div className="enterprise-loader">
      <div className="loader-content">
        {/* Sacred Mandala */}
        <div className="mandala-container">
          <div className="mandala-outer">
            <div className="mandala-middle">
              <div className="mandala-inner">
                <div className="om-symbol">ॐ</div>
              </div>
            </div>
          </div>
        </div>

        {/* Brand */}
        <div className="loader-brand">
          <h1 className="logo">BAKASANA</h1>
          <p className="loader-subtitle">Wewnętrzna podróż</p>
        </div>

        {/* Progress */}
        <div className="loader-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="progress-text subtle-text">
            {progress < 100 ? 'Przygotowujemy duchowe sanktuarium...' : 'Namaste'}
          </div>
        </div>
      </div>

      <style jsx>{`
        .enterprise-loader {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: var(--sanctuary);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 9999;
          opacity: ${isVisible ? 1 : 0};
          transition: opacity 500ms ease;
        }

        .loader-content {
          text-align: center;
          max-width: 400px;
          padding: 0 8%;
        }

        .mandala-container {
          margin-bottom: 48px;
          position: relative;
        }

        .mandala-outer {
          width: 120px;
          height: 120px;
          border: 2px solid var(--temple-gold);
          border-radius: 50%;
          margin: 0 auto;
          position: relative;
          animation: mandala-rotate 8s linear infinite;
          opacity: 0.6;
        }

        .mandala-middle {
          width: 80px;
          height: 80px;
          border: 1px solid var(--sage-green);
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          animation: mandala-rotate-reverse 6s linear infinite;
          opacity: 0.7;
        }

        .mandala-inner {
          width: 40px;
          height: 40px;
          border: 1px solid var(--stone);
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          align-items: center;
          justify-content: center;
          animation: mandala-pulse 3s ease-in-out infinite;
        }

        .om-symbol {
          font-family: 'Noto Sans Devanagari', serif;
          font-size: 18px;
          color: var(--om-symbol);
          opacity: 0.8;
        }

        .loader-brand {
          margin-bottom: 48px;
        }

        .loader-subtitle {
          font-size: 14px;
          font-weight: 300;
          color: var(--stone);
          letter-spacing: 0.3px;
          margin-top: 16px;
        }

        .loader-progress {
          margin-top: 32px;
        }

        .progress-bar {
          width: 200px;
          height: 1px;
          background: var(--stone);
          opacity: 0.3;
          margin: 0 auto 16px auto;
          position: relative;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background: var(--temple-gold);
          transition: width 100ms ease;
        }

        .progress-text {
          font-size: 11px;
          opacity: 0.6;
        }

        @keyframes mandala-rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes mandala-rotate-reverse {
          from { transform: translate(-50%, -50%) rotate(360deg); }
          to { transform: translate(-50%, -50%) rotate(0deg); }
        }

        @keyframes mandala-pulse {
          0%, 100% {
            opacity: 0.6;
            transform: translate(-50%, -50%) scale(1);
          }
          50% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.1);
          }
        }

        @media (prefers-reduced-motion: reduce) {
          .mandala-outer,
          .mandala-middle,
          .mandala-inner {
            animation: none;
          }
        }
      `}</style>
    </div>
  );
};

export default EnterpriseLoader;
